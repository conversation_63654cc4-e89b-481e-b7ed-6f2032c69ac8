if (!customElements.get('price-per-item')) {
    customElements.define(
      'price-per-item',
      class PricePerItem extends HTMLElement {
        constructor() {
          super();
          this.variantId = this.dataset.variantId;
          this.input = document.getElementById(`Quantity-${this.dataset.sectionId || this.dataset.variantId}`);
          if (this.input) {
            this.input.addEventListener('change', this.onInputChange.bind(this));
          }

          // Only initialize wallpaper pricing for wallpaper products
          if (this.isWallpaperProduct()) {
            // Initialize with dynamic rate per sq.ft
            this.updatePricePerItem();

            // Listen for material selection changes
            const materialRadios = document.querySelectorAll('.material-radio');
            materialRadios.forEach(radio => {
              radio.addEventListener('change', () => {
                this.updatePricePerItem();
              });
            });
          }
        }

        // Check if current product is a wallpaper product
        isWallpaperProduct() {
          // Check for wallpaper-specific elements
          return document.querySelector('.material-radio') !== null;
        }
  
        updatePricePerItemUnsubscriber = undefined;
        variantIdChangedUnsubscriber = undefined;
  
        connectedCallback() {
          // Update variantId if variant is switched on product page
          this.variantIdChangedUnsubscriber = subscribe(PUB_SUB_EVENTS.variantChange, (event) => {
            this.variantId = event.data.variant.id.toString();
            this.updatePricePerItem(); // Show fixed rate per sq.ft
          });
  
          this.updatePricePerItemUnsubscriber = subscribe(PUB_SUB_EVENTS.cartUpdate, (response) => {
            if (!response.cartData) return;
  
            // Item was added to cart via product page
            if (response.cartData['variant_id'] !== undefined) {
              if (response.productVariantId === this.variantId) this.updatePricePerItem(response.cartData.quantity);
              // Qty was updated in cart
            } else if (response.cartData.item_count !== 0) {
              const isVariant = response.cartData.items.find((item) => item.variant_id.toString() === this.variantId);
              if (isVariant && isVariant.id.toString() === this.variantId) {
                // The variant is still in cart
                this.updatePricePerItem(isVariant.quantity);
              } else {
                // The variant was removed from cart, qty is 0
                this.updatePricePerItem(0);
              }
              // All items were removed from cart
            } else {
              this.updatePricePerItem(0);
            }
          });
        }
  
        disconnectedCallback() {
          if (this.updatePricePerItemUnsubscriber) {
            this.updatePricePerItemUnsubscriber();
          }
          if (this.variantIdChangedUnsubscriber) {
            this.variantIdChangedUnsubscriber();
          }
        }
  
        onInputChange() {
          this.updatePricePerItem();
        }
  
        updatePricePerItem(updatedCartQuantity = null) {
          const pricePerItemCurrent = document.querySelector(`price-per-item[id^="Price-Per-Item-${this.dataset.sectionId || this.dataset.variantId}"] .price-per-item span`);

          if (pricePerItemCurrent) {
            // For wallpaper products, show dynamic rate per sq.ft based on selected material
            if (this.isWallpaperProduct()) {
              const selectedMaterial = document.querySelector('.material-radio:checked');
              const materialPrice = selectedMaterial ? selectedMaterial.dataset.price : '10';
              pricePerItemCurrent.innerHTML = `₹${materialPrice} per sq.ft`;
            }
            // For non-wallpaper products, use original volume pricing logic would go here
            // (keeping the method functional for other products)
          }
        }
  
        getCartQuantity(updatedCartQuantity) {
          return (updatedCartQuantity || updatedCartQuantity === 0) ? updatedCartQuantity : parseInt(this.input.dataset.cartQuantity);
        }
  
        // Volume pricing removed - using fixed rate per sq.ft for wallpaper products
      }
    );
  }
  