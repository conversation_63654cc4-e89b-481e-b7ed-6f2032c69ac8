<product-info
  id="MainProduct-{{ section.id }}"
  class="section-{{ section.id }}-padding gradient color-{{ section.settings.color_scheme }}"
  data-section="{{ section.id }}"
  data-product-id="{{ product.id }}"
  data-update-url="true"
  data-url="{{ product.url }}"
  {% if section.settings.image_zoom == 'hover' %}
    data-zoom-on-hover
  {% endif %}
>
  {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
  {{ 'component-accordion.css' | asset_url | stylesheet_tag }}
  {{ 'component-price.css' | asset_url | stylesheet_tag }}
  {{ 'component-slider.css' | asset_url | stylesheet_tag }}
  {{ 'component-rating.css' | asset_url | stylesheet_tag }}
  {{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}

  {% unless product.has_only_default_variant %}
    {{ 'component-product-variant-picker.css' | asset_url | stylesheet_tag }}
    {{ 'component-swatch-input.css' | asset_url | stylesheet_tag }}
    {{ 'component-swatch.css' | asset_url | stylesheet_tag }}
  {% endunless %}
  {%- if product.quantity_price_breaks_configured? -%}
    {{ 'component-volume-pricing.css' | asset_url | stylesheet_tag }}
  {%- endif -%}

  {%- style -%}
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }
  {%- endstyle -%}


  <style>
    /* Material Selector Styles */
    .product-form__material-selector {
      margin: 1.5rem 0;
      padding: 1.5rem;
      border: 2px solid #e8f4fd;
      border-radius: 12px;
      background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
      display: none !important;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .material-options {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
      justify-content: center;
    }

    .material-option {
      position: relative;
      flex: 1;
      max-width: 150px;
    }

    .material-radio {
      position: absolute;
      opacity: 0;
      cursor: pointer;
    }

    .material-label {
      display: block;
      padding: 0.75rem 1rem;
      border: 2px solid #ddd;
      border-radius: 8px;
      background: white;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
    }

    .material-label:hover {
      border-color: #3498db;
      box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
    }

    .material-radio:checked + .material-label {
      border-color: #27ae60;
      background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
    }

    .material-info {
      display: block;
    }

    .material-name {
      font-weight: 600;
      font-size: 1rem;
      color: #2c3e50;
      display: block;
    }

    .material-price {
      display: none; /* Hide price in simplified view */
    }

    .material-description {
      display: none; /* Hide description in simplified view */
    }

    /* Custom Dimensions Styles */
    .product-form__custom-dimensions {
      margin: 1.5rem 0;
      padding: 1.5rem;
      border: 2px solid #e8f4fd;
      border-radius: 12px;
      background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
      display: none !important;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .custom-dimensions-wrapper {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
    }

    .custom-dimension-input {
      flex: 1;
    }

    .custom-dimension-input .form__label {
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #2c3e50;
    }

    .dimension-input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #ddd;
      border-radius: 6px;
      font-size: 1rem;
      transition: border-color 0.3s ease;
    }

    .dimension-input:focus {
      border-color: #3498db;
      outline: none;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .dimension-calculation {
      margin-top: 1.5rem;
      padding: 1rem;
      background: white;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
    }

    .calculation-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.75rem;
    }

    .calculation-row:last-child {
      margin-bottom: 0;
    }

    .calculation-label {
      font-weight: 500;
      color: #555;
      min-width: 120px;
    }

    .calculation-value {
      font-weight: 700;
      font-size: 1.1rem;
      color: #2c3e50;
    }

    .calculation-unit {
      font-size: 0.9rem;
      color: #666;
      margin-left: 0.25rem;
    }

    .calculation-currency {
      font-size: 1.25rem;
      font-weight: 700;
      color: #e74c3c;
    }

    .total-row {
      border-top: 2px solid #dee2e6;
      padding-top: 1rem;
      margin-top: 1rem;
    }

    .total-price {
      font-size: 1.5rem !important;
      color: #27ae60 !important;
    }

    @media screen and (max-width: 749px) {
      .custom-dimensions-wrapper {
        flex-direction: column;
        gap: 1rem;
      }

      .calculation-row {
        flex-wrap: wrap;
        justify-content: space-between;
      }

      .calculation-label {
        min-width: auto;
      }

      .material-options {
        flex-direction: row;
        gap: 0.5rem;
      }

      .material-option {
        max-width: none;
        flex: 1;
      }

      .material-label {
        padding: 0.6rem 0.5rem;
        font-size: 0.9rem;
      }
    }
  </style>

  <script src="{{ 'product-info.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
  {%- if product.quantity_price_breaks_configured? -%}
    <script src="{{ 'show-more.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}
  <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>

  {% if section.settings.image_zoom == 'hover' %}
    <script id="EnableZoomOnHover-main" src="{{ 'magnify.js' | asset_url }}" defer="defer"></script>
  {% endif %}
  {%- if request.design_mode -%}
    <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
  {%- endif -%}

  {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}
  {%- if first_3d_model -%}
    {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
    <link
      id="ModelViewerStyle"
      rel="stylesheet"
      href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css"
      media="print"
      onload="this.media='all'"
    >
    <link
      id="ModelViewerOverride"
      rel="stylesheet"
      href="{{ 'component-model-viewer-ui.css' | asset_url }}"
      media="print"
      onload="this.media='all'"
    >
  {%- endif -%}

  {% assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src' %}
  <div class="page-width">
    <div class="product product--{{ section.settings.media_size }} product--{{ section.settings.media_position }} product--{{ section.settings.gallery_layout }} product--mobile-{{ section.settings.mobile_thumbnails }} grid grid--1-col {% if product.media.size > 0 %}grid--2-col-tablet{% else %}product--no-media{% endif %}">
      <div class="grid__item product__media-wrapper">
        {% render 'product-media-gallery', variant_images: variant_images %}
      </div>
      <div class="product__info-wrapper grid__item{% if settings.page_width > 1400 and section.settings.media_size == "small" %} product__info-wrapper--extra-padding{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        <section
          id="ProductInfo-{{ section.id }}"
          class="product__info-container{% if section.settings.enable_sticky_info %} product__column-sticky{% endif %}"
        >
          {%- assign product_form_id = 'product-form-' | append: section.id -%}

          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when '@app' -%}
                {% render block %}
              {%- when 'text' -%}
                <p
                  class="product__text inline-richtext{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}"
                  {{ block.shopify_attributes }}
                >
                  {{- block.settings.text -}}
                </p>
              {%- when 'title' -%}
                <div class="product__title" {{ block.shopify_attributes }}>
                  <h1>{{ product.title | escape }}</h1>
                  <a href="{{ product.url }}" class="product__title">
                    <h2 class="h1">
                      {{ product.title | escape }}
                    </h2>
                  </a>
                </div>
              {%- when 'price' -%}
                <div id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                  {%- render 'price',
                    product: product,
                    use_variant: true,
                    show_badges: true,
                    price_class: 'price--large'
                  -%}
                </div>
                {%- if product.quantity_price_breaks_configured? -%}
                  <div class="volume-pricing-note" id="Volume-Note-{{ section.id }}">
                    <span>{{ 'products.product.volume_pricing.note' | t }}</span>
                  </div>
                {%- endif -%}
                {%- if cart.taxes_included or cart.duties_included or shop.shipping_policy.body != blank -%}
                  <div class="product__tax caption rte">
                    {%- if cart.duties_included and cart.taxes_included -%}
                      {{ 'products.product.duties_and_taxes_included' | t }}
                    {%- elsif cart.taxes_included -%}
                      {{ 'products.product.taxes_included' | t }}
                    {%- elsif cart.duties_included -%}
                      {{ 'products.product.duties_included' | t }}
                    {%- endif -%}
                    {%- if shop.shipping_policy.body != blank -%}
                      {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                    {%- endif -%}
                  </div>
                {%- endif -%}
                <div {{ block.shopify_attributes }}>
                  {%- assign product_form_installment_id = 'product-form-installment-' | append: section.id -%}
                  {%- form 'product', product, id: product_form_installment_id, class: 'installment caption-large' -%}
                    <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                    {{ form | payment_terms }}
                  {%- endform -%}
                </div>
              {%- when 'inventory' -%}
                <p
                  class="product__inventory{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.inventory_management != 'shopify' %} visibility-hidden{% endif %}"
                  {{ block.shopify_attributes }}
                  id="Inventory-{{ section.id }}"
                  role="status"
                >
                  {%- if product.selected_or_first_available_variant.inventory_management == 'shopify' -%}
                    {%- if product.selected_or_first_available_variant.inventory_quantity > 0 -%}
                      {%- if product.selected_or_first_available_variant.inventory_quantity
                          <= block.settings.inventory_threshold
                      -%}
                        <span class="svg-wrapper" style="color: rgb(238, 148, 65)">
                          {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                        </span>
                        {%- if block.settings.show_inventory_quantity -%}
                          {{-
                            'products.product.inventory_low_stock_show_count'
                            | t: quantity: product.selected_or_first_available_variant.inventory_quantity
                          -}}
                        {%- else -%}
                          {{- 'products.product.inventory_low_stock' | t -}}
                        {%- endif -%}
                      {%- else -%}
                        <span class="svg-wrapper" style="color: rgb(62, 214, 96)">
                          {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                        </span>
                        {%- if block.settings.show_inventory_quantity -%}
                          {{-
                            'products.product.inventory_in_stock_show_count'
                            | t: quantity: product.selected_or_first_available_variant.inventory_quantity
                          -}}
                        {%- else -%}
                          {{- 'products.product.inventory_in_stock' | t -}}
                        {%- endif -%}
                      {%- endif -%}
                    {%- else -%}
                      {%- if product.selected_or_first_available_variant.inventory_policy == 'continue' -%}
                        <span class="svg-wrapper" style="color: rgb(62, 214, 96)">
                          {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                        </span>
                        {{- 'products.product.inventory_out_of_stock_continue_selling' | t -}}
                      {%- else -%}
                        <span class="svg-wrapper" style="color: rgb(200, 200, 200)">
                          {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                        </span>
                        {{- 'products.product.inventory_out_of_stock' | t -}}
                      {%- endif -%}
                    {%- endif -%}
                  {%- endif -%}
                </p>
              {%- when 'description' -%}
                {%- if product.description != blank -%}
                  <div class="product__description rte quick-add-hidden" {{ block.shopify_attributes }}>
                    {{ product.description }}
                  </div>
                {%- endif -%}
              {%- when 'sku' -%}
                <p
                  class="product__sku{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.sku.size == 0 %} visibility-hidden{% endif %}"
                  id="Sku-{{ section.id }}"
                  role="status"
                  {{ block.shopify_attributes }}
                >
                  <span class="visually-hidden">{{ 'products.product.sku' | t }}:</span>
                  {{- product.selected_or_first_available_variant.sku -}}
                </p>
              {%- when 'custom_liquid' -%}
                {{ block.settings.custom_liquid }}
              {%- when 'collapsible_tab' -%}
                <div class="product__accordion accordion quick-add-hidden" {{ block.shopify_attributes }}>
                  <details id="Details-{{ block.id }}-{{ section.id }}">
                    <summary>
                      <div class="summary__title">
                        {% render 'icon-accordion', icon: block.settings.icon %}
                        <h2 class="h4 accordion__title inline-richtext">
                          {{ block.settings.heading | default: block.settings.page.title | escape }}
                        </h2>
                      </div>
                      {{- 'icon-caret.svg' | inline_asset_content -}}
                    </summary>
                    <div class="accordion__content rte" id="ProductAccordion-{{ block.id }}-{{ section.id }}">
                      {{ block.settings.content }}
                      {{ block.settings.page.content }}
                    </div>
                  </details>
                </div>
              {%- when 'quantity_selector' -%}
                <div
                  id="Quantity-Form-{{ section.id }}"
                  class="product-form__input product-form__quantity{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} product-form__quantity-top{% endif %}"
                  {{ block.shopify_attributes }}
                >
                  {% comment %} TODO: enable theme-check once `item_count_for_variant` is accepted as valid filter {% endcomment %}
                  {% # theme-check-disable %}
                  {%- assign cart_qty = cart
                    | item_count_for_variant: product.selected_or_first_available_variant.id
                  -%}
                  {% # theme-check-enable %}
                  <label class="quantity__label form__label" for="Quantity-{{ section.id }}">
                    {{ 'products.product.quantity.label' | t }}
                    <span class="quantity__rules-cart{% if cart_qty == 0 %} hidden{% endif %}">
                      {%- render 'loading-spinner' -%}
                      <span
                        >(
                        {{- 'products.product.quantity.in_cart_html' | t: quantity: cart_qty -}}
                        )</span
                      >
                    </span>
                  </label>
                  <div class="price-per-item__container">
                    <quantity-input class="quantity" data-url="{{ product.url }}" data-section="{{ section.id }}">
                      <button class="quantity__button" name="minus" type="button">
                        <span class="visually-hidden">
                          {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                        </span>
                        <span class="svg-wrapper">
                          {{- 'icon-minus.svg' | inline_asset_content -}}
                        </span>
                      </button>
                      <input
                        class="quantity__input"
                        type="number"
                        name="quantity"
                        id="Quantity-{{ section.id }}"
                        data-cart-quantity="{{ cart_qty }}"
                        data-min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                        min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                        {% if product.selected_or_first_available_variant.quantity_rule.max != null %}
                          data-max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                          max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                        {% endif %}
                        step="{{ product.selected_or_first_available_variant.quantity_rule.increment }}"
                        value="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                        form="{{ product_form_id }}"
                      >
                      <button class="quantity__button" name="plus" type="button">
                        <span class="visually-hidden">
                          {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                        </span>
                        <span class="svg-wrapper">
                          {{- 'icon-plus.svg' | inline_asset_content -}}
                        </span>
                      </button>
                    </quantity-input>
                    {%- liquid
                      assign volume_pricing_array = product.selected_or_first_available_variant.quantity_price_breaks | sort: 'quantity' | reverse
                      assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.min
                      if cart_qty > 0
                        assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.increment
                      endif
                    -%}
                    {%- if product.quantity_price_breaks_configured? -%}
                      <price-per-item
                        id="Price-Per-Item-{{ section.id }}"
                        data-section-id="{{ section.id }}"
                        data-variant-id="{{ product.selected_or_first_available_variant.id }}"
                      >
                        {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                          {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                          <div class="price-per-item">
                            {%- if variant_price_compare -%}
                              <dl class="price-per-item--current">
                                <dt class="visually-hidden">
                                  {{ 'products.product.price.regular_price' | t }}
                                </dt>
                                <dd>
                                  <s class="variant-item__old-price">
                                    {{ variant_price_compare | money_with_currency }}
                                  </s>
                                </dd>
                              </dl>
                            {%- endif -%}
                            {%- if current_qty_for_volume_pricing < volume_pricing_array.last.minimum_quantity -%}
                              {%- assign variant_price = product.selected_or_first_available_variant.price
                                | money_with_currency
                              -%}
                              <span class="price-per-item--current">
                                {{- 'products.product.volume_pricing.price_at_each' | t: price: variant_price -}}
                              </span>
                            {%- else -%}
                              {%- for price_break in volume_pricing_array -%}
                                {%- if current_qty_for_volume_pricing >= price_break.minimum_quantity -%}
                                  {%- assign price_break_price = price_break.price | money_with_currency -%}
                                  <span class="price-per-item--current">
                                    {{-
                                      'products.product.volume_pricing.price_at_each'
                                      | t: price: price_break_price
                                    -}}
                                  </span>
                                  {%- break -%}
                                {%- endif -%}
                              {%- endfor -%}
                            {%- endif -%}
                          </div>
                        {%- else -%}
                          {%- assign variant_price = product.selected_or_first_available_variant.price
                            | money_with_currency
                          -%}
                          {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                          <div class="price-per-item">
                            {%- if variant_price_compare -%}
                              <dl class="price-per-item--current">
                                <dt class="visually-hidden">
                                  {{ 'products.product.price.regular_price' | t }}
                                </dt>
                                <dd>
                                  <s class="variant-item__old-price">
                                    {{ variant_price_compare | money_with_currency }}
                                  </s>
                                </dd>
                                <dt class="visually-hidden">
                                  {{ 'products.product.price.sale_price' | t }}
                                </dt>
                                <dd>
                                  <span class="price-per-item--current">
                                    {{- 'products.product.volume_pricing.price_at_each' | t: price: variant_price -}}
                                  </span>
                                </dd>
                              </dl>
                            {%- else -%}
                              <span class="price-per-item--current">
                                {{- 'products.product.volume_pricing.price_at_each' | t: price: variant_price -}}
                              </span>
                            {%- endif -%}
                          </div>
                        {%- endif -%}
                      </price-per-item>
                    {%- endif -%}
                  </div>
                  <div class="quantity__rules caption" id="Quantity-Rules-{{ section.id }}">
                    {%- if product.selected_or_first_available_variant.quantity_rule.increment > 1 -%}
                      <span class="divider">
                        {{-
                          'products.product.quantity.multiples_of'
                          | t: quantity: product.selected_or_first_available_variant.quantity_rule.increment
                        -}}
                      </span>
                    {%- endif -%}
                    {%- if product.selected_or_first_available_variant.quantity_rule.min > 1 -%}
                      <span class="divider">
                        {{-
                          'products.product.quantity.minimum_of'
                          | t: quantity: product.selected_or_first_available_variant.quantity_rule.min
                        -}}
                      </span>
                    {%- endif -%}
                    {%- if product.selected_or_first_available_variant.quantity_rule.max != null -%}
                      <span class="divider">
                        {{-
                          'products.product.quantity.maximum_of'
                          | t: quantity: product.selected_or_first_available_variant.quantity_rule.max
                        -}}
                      </span>
                    {%- endif -%}
                  </div>
                  {%- if product.quantity_price_breaks_configured? -%}
                    <volume-pricing class="parent-display" id="Volume-{{ section.id }}">
                      {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                        <span class="caption-large">{{ 'products.product.volume_pricing.title' | t }}</span>
                        <ul class="list-unstyled">
                          <li>
                            <span>{{ product.selected_or_first_available_variant.quantity_rule.min }}+</span>
                            {%- assign price = product.selected_or_first_available_variant.price
                              | money_with_currency
                            -%}
                            <span data-text="{{ 'products.product.volume_pricing.price_at_each' | t: price: variant_price }}">
                              {{- 'sections.quick_order_list.each' | t: money: price -}}
                            </span>
                          </li>
                          {%- for price_break in product.selected_or_first_available_variant.quantity_price_breaks -%}
                            {%- assign price_break_price = price_break.price | money_with_currency -%}
                            <li class="{%- if forloop.index >= 3 -%}show-more-item hidden{%- endif -%}">
                              <span>
                                {{- price_break.minimum_quantity -}}
                                <span aria-hidden="true">+</span></span
                              >
                              <span data-text="{{ 'products.product.volume_pricing.price_at_each' | t: price: price_break_price }}">
                                {{- 'sections.quick_order_list.each' | t: money: price_break_price -}}
                              </span>
                            </li>
                          {%- endfor -%}
                        </ul>
                        {%- if product.selected_or_first_available_variant.quantity_price_breaks.size >= 3 -%}
                          <show-more-button>
                            <button
                              class="button-show-more link underlined-link"
                              id="Show-More-{{ section.id }}"
                              type="button"
                            >
                              <span class="label-show-more label-text"
                                ><span aria-hidden="true">+ </span>{{ 'products.facets.show_more' | t }}
                              </span>
                            </button>
                          </show-more-button>
                        {%- endif -%}
                      {%- endif -%}
                    </volume-pricing>
                  {%- endif -%}
                </div>
              {%- when 'popup' -%}
                <modal-opener
                  class="product-popup-modal__opener quick-add-hidden"
                  data-modal="#PopupModal-{{ block.id }}"
                  {{ block.shopify_attributes }}
                >
                  <button
                    id="ProductPopup-{{ block.id }}"
                    class="product-popup-modal__button link"
                    type="button"
                    aria-haspopup="dialog"
                  >
                    {{ block.settings.text | default: block.settings.page.title | escape }}
                  </button>
                </modal-opener>
              {%- when 'share' -%}
                {% liquid
                  assign share_url = product.selected_variant.url | default: product.url | prepend: request.origin
                  render 'share-button', block: block, share_link: share_url
                %}

              {%- when 'variant_picker' -%}
                {% render 'product-variant-picker', product: product, block: block, product_form_id: product_form_id %}
              {%- when 'material_selector' -%}
                <div class="product-form__input product-form__material-selector" {{ block.shopify_attributes }}>
                  <fieldset class="js product-form__input">
                    <legend class="form__label">{{ block.settings.heading | default: 'Select Material Type' }}</legend>

                    <div class="material-options">
                      <div class="material-option">
                        <input
                          type="radio"
                          id="material-basic-{{ section.id }}"
                          name="properties[Material]"
                          value="{{ block.settings.material_1_name | default: 'Basic' }}"
                          data-price="{{ block.settings.material_1_price | default: 10 }}"
                          class="material-radio"
                          checked
                        >
                        <label for="material-basic-{{ section.id }}" class="material-label">
                          <div class="material-info">
                            <span class="material-name">{{ block.settings.material_1_name | default: 'Basic' }}</span>
                            <span class="material-price">₹{{ block.settings.material_1_price | default: 10 }} per sq.ft</span>
                          </div>
                          {% if block.settings.material_1_description %}
                            <p class="material-description">{{ block.settings.material_1_description }}</p>
                          {% endif %}
                        </label>
                      </div>

                      <div class="material-option">
                        <input
                          type="radio"
                          id="material-premium-{{ section.id }}"
                          name="properties[Material]"
                          value="{{ block.settings.material_2_name | default: 'Premium' }}"
                          data-price="{{ block.settings.material_2_price | default: 15 }}"
                          class="material-radio"
                        >
                        <label for="material-premium-{{ section.id }}" class="material-label">
                          <div class="material-info">
                            <span class="material-name">{{ block.settings.material_2_name | default: 'Premium' }}</span>
                            <span class="material-price">₹{{ block.settings.material_2_price | default: 15 }} per sq.ft</span>
                          </div>
                          {% if block.settings.material_2_description %}
                            <p class="material-description">{{ block.settings.material_2_description }}</p>
                          {% endif %}
                        </label>
                      </div>

                      <div class="material-option">
                        <input
                          type="radio"
                          id="material-luxury-{{ section.id }}"
                          name="properties[Material]"
                          value="{{ block.settings.material_3_name | default: 'Luxury' }}"
                          data-price="{{ block.settings.material_3_price | default: 20 }}"
                          class="material-radio"
                        >
                        <label for="material-luxury-{{ section.id }}" class="material-label">
                          <div class="material-info">
                            <span class="material-name">{{ block.settings.material_3_name | default: 'Luxury' }}</span>
                            <span class="material-price">₹{{ block.settings.material_3_price | default: 20 }} per sq.ft</span>
                          </div>
                          {% if block.settings.material_3_description %}
                            <p class="material-description">{{ block.settings.material_3_description }}</p>
                          {% endif %}
                        </label>
                      </div>
                    </div>
                  </fieldset>
                </div>
              {%- when 'custom_dimensions' -%}
                <div class="product-form__input product-form__custom-dimensions" {{ block.shopify_attributes }}>
                  <fieldset class="js product-form__input">
                    <legend class="form__label">{{ block.settings.heading | default: 'Enter Wall Dimensions' }}</legend>

                    <div class="custom-dimensions-wrapper">
                      <div class="custom-dimension-input">
                        <label class="form__label" for="custom-width-{{ section.id }}">
                          {{ block.settings.width_label | default: 'Width' }} (ft)
                        </label>
                        <input
                          type="number"
                          id="custom-width-{{ section.id }}"
                          name="properties[Width]"
                          class="field__input dimension-input"
                          min="{{ block.settings.width_min | default: 1 }}"
                          max="{{ block.settings.width_max | default: 50 }}"
                          step="{{ block.settings.width_step | default: 0.1 }}"
                          placeholder="{{ block.settings.width_placeholder | default: 'e.g. 10' }}"
                          required
                        >
                      </div>
                      <div class="custom-dimension-input">
                        <label class="form__label" for="custom-height-{{ section.id }}">
                          {{ block.settings.height_label | default: 'Height' }} (ft)
                        </label>
                        <input
                          type="number"
                          id="custom-height-{{ section.id }}"
                          name="properties[Height]"
                          class="field__input dimension-input"
                          min="{{ block.settings.height_min | default: 1 }}"
                          max="{{ block.settings.height_max | default: 20 }}"
                          step="{{ block.settings.height_step | default: 0.1 }}"
                          placeholder="{{ block.settings.height_placeholder | default: 'e.g. 8' }}"
                          required
                        >
                      </div>
                    </div>

                    <!-- Dynamic Calculation Display -->
                    <div class="dimension-calculation">
                      <div class="calculation-row">
                        <span class="calculation-label"><strong>Area:</strong></span>
                        <span id="calculated-area-{{ section.id }}" class="calculation-value">0</span>
                        <span class="calculation-unit">sq.ft</span>
                      </div>
                      <div class="calculation-row">
                        <span class="calculation-label"><strong>Rate:</strong></span>
                        <span class="calculation-currency">₹</span>
                        <span id="current-rate-{{ section.id }}" class="calculation-value">10</span>
                        <span class="calculation-unit">per sq.ft</span>
                      </div>
                      <div class="calculation-row total-row">
                        <span class="calculation-label"><strong>Total Price:</strong></span>
                        <span class="calculation-currency">₹</span>
                        <span id="calculated-price-{{ section.id }}" class="calculation-value total-price">0</span>
                      </div>
                    </div>

                    <!-- Hidden inputs for calculated values -->
                    <input type="hidden" id="total-area-{{ section.id }}" name="properties[Total Area]" value="">
                    <input type="hidden" id="selected-rate-{{ section.id }}" name="properties[Rate per sq.ft]" value="">
                    <input type="hidden" id="total-price-{{ section.id }}" name="properties[Total Price]" value="">
                  </fieldset>
                </div>
              {%- when 'buy_buttons' -%}
                {%- render 'buy-buttons',
                  block: block,
                  product: product,
                  product_form_id: product_form_id,
                  section_id: section.id,
                  show_pickup_availability: true
                -%}
              {%- when 'rating' -%}
                {%- if product.metafields.reviews.rating.value != blank -%}
                  <div class="rating-wrapper">
                    {% liquid
                      assign rating_decimal = 0
                      assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
                      if decimal >= 0.3 and decimal <= 0.7
                        assign rating_decimal = 0.5
                      elsif decimal > 0.7
                        assign rating_decimal = 1
                      endif
                    %}
                    <div
                      class="rating"
                      role="img"
                      aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
                    >
                      <span
                        aria-hidden="true"
                        class="rating-star"
                        style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
                      ></span>
                    </div>
                    <p class="rating-text caption">
                      <span aria-hidden="true">
                        {{- product.metafields.reviews.rating.value }} /
                        {{ product.metafields.reviews.rating.value.scale_max -}}
                      </span>
                    </p>
                    <p class="rating-count caption">
                      <span aria-hidden="true">({{ product.metafields.reviews.rating_count }})</span>
                      <span class="visually-hidden">
                        {{- product.metafields.reviews.rating_count }}
                        {{ 'accessibility.total_reviews' | t -}}
                      </span>
                    </p>
                  </div>
                {%- endif -%}
              {%- when 'complementary' -%}
                <product-recommendations
                  class="complementary-products quick-add-hidden{% if block.settings.make_collapsible_row %} is-accordion{% endif %}{% if block.settings.enable_quick_add %} complementary-products-contains-quick-add{% endif %}"
                  data-url="{{ routes.product_recommendations_url }}?limit={{ block.settings.product_list_limit }}&intent=complementary"
                  data-section-id="{{ section.id }}"
                  data-product-id="{{ product.id }}"
                >
                  {%- if recommendations.performed and recommendations.products_count > 0 -%}
                    <aside
                      aria-label="{{ 'accessibility.complementary_products' | t }}"
                      {{ block.shopify_attributes -}}
                      {% if block.settings.make_collapsible_row %}
                        class="product__accordion accordion"
                      {% endif %}
                    >
                      <div class="complementary-products__container">
                        {%- if block.settings.make_collapsible_row -%}
                          <details id="Details-{{ block.id }}-{{ section.id }}" open>
                            <summary>
                        {%- endif %}
                        <div class="summary__title">
                          {%- if block.settings.make_collapsible_row -%}
                            {% render 'icon-accordion', icon: block.settings.icon %}
                            <h2 class="h4 accordion__title">{{ block.settings.block_heading }}</h2>
                          {%- else -%}
                            <h2 class="h3 accordion__title">{{ block.settings.block_heading }}</h2>
                          {%- endif -%}
                        </div>
                        {%- if block.settings.make_collapsible_row -%}
                          {{- 'icon-caret.svg' | inline_asset_content -}}
                          </summary>
                        {%- endif -%}
                        <slideshow-component class="slider-mobile-gutter">
                          {%- assign number_of_slides = recommendations.products_count
                            | plus: 0.0
                            | divided_by: block.settings.products_per_page
                            | ceil
                          -%}
                          <div
                            id="Slider-{{ block.id }}"
                            class="contains-card contains-card--product complementary-slider grid grid--1-col slider slider--everywhere"
                            role="list"
                            {% if number_of_slides > 1 %}
                              aria-label="{{ 'general.slider.name' | t }}"
                            {% endif %}
                          >
                            {% assign skip_card_product_styles = false %}
                            {%- for i in (1..number_of_slides) -%}
                              <div
                                id="Slide-{{ block.id }}-{{ forloop.index }}"
                                class="complementary-slide complementary-slide--{{ settings.card_style }} grid__item slider__slide slideshow__slide"
                                tabindex="-1"
                                role="group"
                                {% if number_of_slides > 1 %}
                                  aria-roledescription="{{ 'sections.slideshow.slide' | t }}"
                                  aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                                {% endif %}
                              >
                                <ul class="list-unstyled" role="list">
                                  {%- for product in recommendations.products
                                    limit: block.settings.products_per_page
                                    offset: continue
                                  -%}
                                    <li>
                                      {%- if block.settings.enable_quick_add -%}
                                        {% assign quick_add = 'standard' %}
                                      {%- else -%}
                                        {% assign quick_add = 'none' %}
                                      {%- endif -%}
                                      {% render 'card-product',
                                        card_product: product,
                                        media_aspect_ratio: block.settings.image_ratio,
                                        show_secondary_image: false,
                                        lazy_load: false,
                                        skip_styles: skip_card_product_styles,
                                        quick_add: quick_add,
                                        section_id: section.id,
                                        horizontal_class: true,
                                        horizontal_quick_add: true
                                      %}
                                    </li>
                                    {%- assign skip_card_product_styles = true -%}
                                  {%- endfor -%}
                                </ul>
                              </div>
                            {%- endfor -%}
                          </div>
                          {%- if number_of_slides > 1 -%}
                            <div class="slider-buttons">
                              <button
                                type="button"
                                class="slider-button slider-button--prev"
                                name="previous"
                                aria-label="{{ 'general.slider.previous_slide' | t }}"
                              >
                                <span class="svg-wrapper">
                                  {{- 'icon-caret.svg' | inline_asset_content -}}
                                </span>
                              </button>
                              <div class="slider-counter slider-counter--{{ block.settings.pagination_style }}{% if block.settings.pagination_style == 'counter' or block.settings.pagination_style == 'numbers' %} caption{% endif %}">
                                {%- if block.settings.pagination_style == 'counter' -%}
                                  <span class="slider-counter--current">1</span>
                                  <span aria-hidden="true"> / </span>
                                  <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                                  <span class="slider-counter--total">{{ number_of_slides }}</span>
                                {%- else -%}
                                  <div class="slideshow__control-wrapper">
                                    {%- for i in (1..number_of_slides) -%}
                                      <button
                                        class="slider-counter__link slider-counter__link--{{ block.settings.pagination_style }} link"
                                        aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                                        aria-controls="Slider-{{ block.id }}"
                                      >
                                        {%- if block.settings.pagination_style == 'numbers' -%}
                                          {{ forloop.index -}}
                                        {%- else -%}
                                          <span class="dot"></span>
                                        {%- endif -%}
                                      </button>
                                    {%- endfor -%}
                                  </div>
                                {%- endif -%}
                              </div>
                              <button
                                type="button"
                                class="slider-button slider-button--next"
                                name="next"
                                aria-label="{{ 'general.slider.next_slide' | t }}"
                              >
                                <span class="svg-wrapper">
                                  {{- 'icon-caret.svg' | inline_asset_content -}}
                                </span>
                              </button>
                            </div>
                          {%- endif -%}
                        </slideshow-component>
                        {%- if block.settings.make_collapsible_row -%}
                          </details>
                        {%- endif -%}
                      </div>
                    </aside>
                  {%- endif -%}
                  {{ 'component-card.css' | asset_url | stylesheet_tag }}
                  {{ 'component-complementary-products.css' | asset_url | stylesheet_tag }}
                  {%- if block.settings.enable_quick_add -%}
                    {{ 'quick-add.css' | asset_url | stylesheet_tag }}
                    <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
                  {%- endif -%}
                </product-recommendations>
              {%- when 'icon-with-text' -%}
                {% render 'icon-with-text', block: block %}
              {%- when 'wallpaper_test' -%}
                <div class="wallpaper-test-block" {{ block.shopify_attributes }}>
                  <h3>🧪 WALLPAPER DETECTION</h3>
                  <p><strong>Tags:</strong> {{ product.tags | join: ', ' }}</p>
                  <p><strong>Type:</strong> {{ product.type }}</p>
                  <p><strong>Handle:</strong> {{ product.handle }}</p>
                  <p><strong>Status:</strong> <span id="detection-status">...</span></p>
                  <p><strong>Features:</strong> <span id="features-status">...</span></p>
                  <style>
                    .wallpaper-test-block {
                      background: #f0f8ff;
                      border: 2px solid #007acc;
                      padding: 1rem;
                      margin: 1rem 0;
                      border-radius: 8px;
                      transition: all 0.3s ease;
                      font-family: monospace;
                      font-size: 0.9rem;
                    }
                    .wallpaper-test-block strong {
                      color: #333;
                    }
                  </style>
                </div>
            {%- endcase -%}
          {%- endfor -%}
          <a href="{{ product.url }}" class="link product__view-details animate-arrow">
            {{ 'products.product.view_full_details' | t }}
            {{- 'icon-arrow.svg' | inline_asset_content -}}
          </a>
        </section>
      </div>
    </div>

    {% render 'product-media-modal', variant_images: variant_images %}

    {% assign popups = section.blocks | where: 'type', 'popup' %}
    {%- for block in popups -%}
      <modal-dialog id="PopupModal-{{ block.id }}" class="product-popup-modal" {{ block.shopify_attributes }}>
        <div
          role="dialog"
          aria-label="{{ block.settings.text }}"
          aria-modal="true"
          class="product-popup-modal__content"
          tabindex="-1"
        >
          <button
            id="ModalClose-{{ block.id }}"
            type="button"
            class="product-popup-modal__toggle"
            aria-label="{{ 'accessibility.close' | t }}"
          >
            {{- 'icon-close.svg' | inline_asset_content -}}
          </button>
          <div class="product-popup-modal__content-info">
            <h1 class="h2">{{ block.settings.page.title | escape }}</h1>
            {{ block.settings.page.content }}
          </div>
        </div>
      </modal-dialog>
    {%- endfor -%}

    {%- if product.media.size > 0 -%}
      <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
      <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {%- if first_3d_model -%}
      <script type="application/json" id="ProductJSON-{{ product.id }}">
        {{ product.media | where: 'media_type', 'model' | json }}
      </script>
      <script src="{{ 'product-model.js' | asset_url }}" defer></script>
    {%- endif -%}

    {%- liquid
      if product.selected_or_first_available_variant.featured_media
        assign seo_media = product.selected_or_first_available_variant.featured_media
      else
        assign seo_media = product.featured_media
      endif
    -%}

    <script type="application/ld+json">
      {{ product | structured_data }}
    </script>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if current product is a wallpaper product
      function isWallpaperProduct() {
        // Method 1: Get product data from JSON
        const productDataElement = document.querySelector('[data-product-json]');
        if (productDataElement) {
          try {
            const productData = JSON.parse(productDataElement.textContent);
            const tags = productData.tags || [];
            const productType = productData.product_type || '';

            console.log('📄 Product Data Found:', { tags, productType });

            // Check for wallpaper in tags or product type (case insensitive)
            const tagMatch = tags.some(tag => {
              const lowerTag = tag.toLowerCase();
              return lowerTag.includes('wallpaper') ||
                     lowerTag.includes('wall paper') ||
                     lowerTag.includes('wallpapers') ||
                     lowerTag === 'wallpaper' ||
                     lowerTag === 'wallpapers';
            });

            const typeMatch = productType.toLowerCase().includes('wallpaper') ||
                             productType.toLowerCase().includes('wallpapers') ||
                             productType.toLowerCase() === 'wallpaper' ||
                             productType.toLowerCase() === 'wallpapers';

            const isWallpaper = tagMatch || typeMatch;

            console.log('🏷️ Product Tags:', tags);
            console.log('📦 Product Type:', productType);
            console.log('🔍 Tag Match:', tagMatch);
            console.log('🔍 Type Match:', typeMatch);
            console.log('🎯 Is Wallpaper:', isWallpaper);

            return isWallpaper;
          } catch (e) {
            console.warn('⚠️ Could not parse product data:', e);
          }
        }

        // Method 2: Check structured data
        const structuredData = document.querySelector('script[type="application/ld+json"]');
        if (structuredData) {
          try {
            const data = JSON.parse(structuredData.textContent);
            if (data.category && data.category.toLowerCase().includes('wallpaper')) {
              console.log('📊 Found in structured data');
              return true;
            }
          } catch (e) {
            console.log('⚠️ Could not parse structured data');
          }
        }

        // Method 3: Fallback - check URL, page title, or product handle
        const url = window.location.href.toLowerCase();
        const title = document.title.toLowerCase();
        const handle = window.location.pathname.toLowerCase();
        const fallbackResult = url.includes('wallpaper') ||
               title.includes('wallpaper') ||
               handle.includes('wallpaper') ||
               url.includes('wall-paper') ||
               handle.includes('wall-paper');

        console.log('🔍 Fallback check:', { url, title, handle, result: fallbackResult });
        return fallbackResult;
      }

      // Show/hide wallpaper features based on product type
      function toggleWallpaperFeatures() {
        const materialSelector = document.querySelector('.product-form__material-selector');
        const dimensionCalculator = document.querySelector('.product-form__custom-dimensions');
        const wallpaperTestBlock = document.querySelector('.wallpaper-test-block');
        const isWallpaper = isWallpaperProduct();

        console.log('🔄 Toggling features for wallpaper:', isWallpaper);
        console.log('📦 Elements found:', {
          materialSelector: !!materialSelector,
          dimensionCalculator: !!dimensionCalculator,
          wallpaperTestBlock: !!wallpaperTestBlock
        });

        if (isWallpaper) {
          console.log('✅ Wallpaper detected - enabling features');
          if (materialSelector) {
            materialSelector.style.setProperty('display', 'block', 'important');
            materialSelector.style.opacity = '1';
            materialSelector.style.visibility = 'visible';
          }
          if (dimensionCalculator) {
            dimensionCalculator.style.setProperty('display', 'block', 'important');
            dimensionCalculator.style.opacity = '1';
            dimensionCalculator.style.visibility = 'visible';
          }
          if (wallpaperTestBlock) {
            wallpaperTestBlock.style.backgroundColor = '#d4edda';
            wallpaperTestBlock.style.borderColor = '#28a745';
          }
        } else {
          console.log('❌ Not wallpaper - hiding features');
          if (materialSelector) {
            materialSelector.style.setProperty('display', 'none', 'important');
            materialSelector.style.opacity = '0';
            materialSelector.style.visibility = 'hidden';
          }
          if (dimensionCalculator) {
            dimensionCalculator.style.setProperty('display', 'none', 'important');
            dimensionCalculator.style.opacity = '0';
            dimensionCalculator.style.visibility = 'hidden';
          }
          if (wallpaperTestBlock) {
            wallpaperTestBlock.style.backgroundColor = '#f8d7da';
            wallpaperTestBlock.style.borderColor = '#dc3545';
          }
        }
      }

      // Initialize wallpaper features if it's a wallpaper product
      if (isWallpaperProduct()) {
        // Initialize material-based pricing system for wallpaper products
        const materialRadios = document.querySelectorAll('.material-radio');
        const pricePerItemElements = document.querySelectorAll('.price-per-item span');
        const widthInput = document.getElementById('custom-width-{{ section.id }}');
        const heightInput = document.getElementById('custom-height-{{ section.id }}');
        const areaDisplay = document.getElementById('calculated-area-{{ section.id }}');
        const rateDisplay = document.getElementById('current-rate-{{ section.id }}');
        const priceDisplay = document.getElementById('calculated-price-{{ section.id }}');
        const totalAreaInput = document.getElementById('total-area-{{ section.id }}');
        const selectedRateInput = document.getElementById('selected-rate-{{ section.id }}');
        const totalPriceInput = document.getElementById('total-price-{{ section.id }}');

      function getSelectedMaterialPrice() {
        const selectedMaterial = document.querySelector('.material-radio:checked');
        return selectedMaterial ? parseFloat(selectedMaterial.dataset.price) || 10 : 10;
      }

      function updatePricePerItemDisplay() {
        const selectedMaterial = document.querySelector('.material-radio:checked');
        const materialPrice = selectedMaterial ? selectedMaterial.dataset.price : '10';

        pricePerItemElements.forEach(element => {
          element.innerHTML = `₹${materialPrice} per sq.ft`;
        });
      }

      function calculateWallpaperPrice() {
        const width = parseFloat(widthInput?.value) || 0;
        const height = parseFloat(heightInput?.value) || 0;
        const area = width * height;
        const pricePerSqFt = getSelectedMaterialPrice();
        const totalPrice = area * pricePerSqFt;

        // Update display elements
        if (areaDisplay) areaDisplay.textContent = area.toFixed(2);
        if (rateDisplay) rateDisplay.textContent = pricePerSqFt.toString();
        if (priceDisplay) priceDisplay.textContent = totalPrice.toFixed(0);

        // Update hidden inputs for form submission
        if (totalAreaInput) totalAreaInput.value = area.toFixed(2);
        if (selectedRateInput) selectedRateInput.value = pricePerSqFt.toString();
        if (totalPriceInput) totalPriceInput.value = totalPrice.toFixed(0);

        // Update price-per-item display
        updatePricePerItemDisplay();
      }

      // Add event listeners for dimension inputs
      if (widthInput && heightInput) {
        widthInput.addEventListener('input', calculateWallpaperPrice);
        heightInput.addEventListener('input', calculateWallpaperPrice);
      }

      // Add event listeners for material selection
      materialRadios.forEach(radio => {
        radio.addEventListener('change', calculateWallpaperPrice);
      });

        // Initial calculation
        calculateWallpaperPrice();
      }

      // Run detection and toggle features
      toggleWallpaperFeatures();

      // Update test block status
      const detectionStatus = document.getElementById('detection-status');
      const featuresStatus = document.getElementById('features-status');
      const isWallpaper = isWallpaperProduct();

      if (detectionStatus) {
        detectionStatus.textContent = isWallpaper ? '✅ Detected' : '❌ Not Detected';
        detectionStatus.style.color = isWallpaper ? '#28a745' : '#dc3545';
      }
      if (featuresStatus) {
        featuresStatus.textContent = isWallpaper ? '✅ Enabled' : '❌ Hidden';
        featuresStatus.style.color = isWallpaper ? '#28a745' : '#dc3545';
      }
    });
  </script>
</product-info>

{% schema %}
{
  "name": "t:sections.main-product.name",
  "tag": "section",
  "class": "section",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "text",
      "name": "t:sections.main-product.blocks.text.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "t:sections.main-product.blocks.text.settings.text.default",
          "label": "t:sections.main-product.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-product.blocks.title.name",
      "limit": 1
    },
    {
      "type": "price",
      "name": "t:sections.main-product.blocks.price.name",
      "limit": 1
    },
    {
      "type": "sku",
      "name": "t:sections.main-product.blocks.sku.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.sku.settings.text_style.label"
        }
      ]
    },
    {
      "type": "inventory",
      "name": "t:sections.main-product.blocks.inventory.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.inventory.settings.text_style.label"
        },
        {
          "type": "range",
          "id": "inventory_threshold",
          "label": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.label",
          "min": 0,
          "max": 100,
          "step": 1,
          "info": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.info",
          "default": 10
        },
        {
          "type": "checkbox",
          "id": "show_inventory_quantity",
          "label": "t:sections.main-product.blocks.inventory.settings.show_inventory_quantity.label",
          "default": true
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "t:sections.main-product.blocks.quantity_selector.name",
      "limit": 1
    },
    {
      "type": "variant_picker",
      "name": "t:sections.main-product.blocks.variant_picker.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "picker_type",
          "options": [
            {
              "value": "dropdown",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__1.label"
            },
            {
              "value": "button",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__2.label"
            }
          ],
          "default": "button",
          "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.label"
        },
        {
          "id": "swatch_shape",
          "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.label",
          "type": "select",
          "info": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.info",
          "options": [
            {
              "value": "circle",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__1.label"
            },
            {
              "value": "square",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__2.label"
            },
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__3.label"
            }
          ],
          "default": "circle"
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "t:sections.main-product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "default": true,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "default": true,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.info"
        }
      ]
    },
    {
      "type": "description",
      "name": "t:sections.main-product.blocks.description.name",
      "limit": 1
    },
    {
      "type": "share",
      "name": "t:sections.main-product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "share_label",
          "label": "t:sections.main-product.blocks.share.settings.text.label",
          "default": "t:sections.main-product.blocks.share.settings.text.default"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.share.settings.title_info.content"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "collapsible_tab",
      "name": "t:sections.main-product.blocks.collapsible_tab.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "t:sections.main-product.blocks.collapsible_tab.settings.heading.default",
          "info": "t:sections.main-product.blocks.collapsible_tab.settings.heading.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "check_mark",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
        }
      ]
    },
    {
      "type": "popup",
      "name": "t:sections.main-product.blocks.popup.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "t:sections.main-product.blocks.popup.settings.link_label.default",
          "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.main-product.blocks.popup.settings.page.label"
        }
      ]
    },
    {
      "type": "rating",
      "name": "t:sections.main-product.blocks.rating.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.rating.settings.paragraph.content"
        }
      ]
    },
    {
      "type": "complementary",
      "name": "t:sections.main-product.blocks.complementary_products.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.complementary_products.settings.paragraph.content"
        },
        {
          "type": "text",
          "id": "block_heading",
          "default": "t:sections.main-product.blocks.complementary_products.settings.heading.default",
          "label": "t:sections.main-product.blocks.complementary_products.settings.heading.label"
        },
        {
          "type": "checkbox",
          "id": "make_collapsible_row",
          "default": false,
          "label": "t:sections.main-product.blocks.complementary_products.settings.make_collapsible_row.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "price_tag",
          "info": "t:sections.main-product.blocks.complementary_products.settings.icon.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "range",
          "id": "product_list_limit",
          "min": 1,
          "max": 10,
          "step": 1,
          "default": 10,
          "label": "t:sections.main-product.blocks.complementary_products.settings.product_list_limit.label"
        },
        {
          "type": "range",
          "id": "products_per_page",
          "min": 1,
          "max": 4,
          "step": 1,
          "default": 3,
          "label": "t:sections.main-product.blocks.complementary_products.settings.products_per_page.label"
        },
        {
          "type": "select",
          "id": "pagination_style",
          "options": [
            {
              "value": "dots",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_1"
            },
            {
              "value": "counter",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_2"
            },
            {
              "value": "numbers",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_3"
            }
          ],
          "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.label",
          "default": "counter"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.complementary_products.settings.product_card.heading"
        },
        {
          "type": "select",
          "id": "image_ratio",
          "options": [
            {
              "value": "portrait",
              "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_1"
            },
            {
              "value": "square",
              "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.options.option_2"
            }
          ],
          "label": "t:sections.main-product.blocks.complementary_products.settings.image_ratio.label",
          "default": "square"
        },
        {
          "type": "checkbox",
          "id": "enable_quick_add",
          "label": "t:sections.main-product.blocks.complementary_products.settings.enable_quick_add.label",
          "default": false
        }
      ]
    },
    {
      "type": "icon-with-text",
      "name": "t:sections.main-product.blocks.icon_with_text.name",
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "options": [
            {
              "value": "horizontal",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__1.label"
            },
            {
              "value": "vertical",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__2.label"
            }
          ],
          "default": "horizontal",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.label"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.icon_with_text.settings.content.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.content.info"
        },
        {
          "type": "select",
          "id": "icon_1",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "heart",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_1.label"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_1.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_1",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_1.default",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_1.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "select",
          "id": "icon_2",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "return",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_2.label"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_2.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_2",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_2.default",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_2.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        },
        {
          "type": "select",
          "id": "icon_3",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "truck",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_3.label"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_3.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_3",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_3.default",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.heading_3.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.heading.info"
        }
      ]
    },
    {
      "type": "material_selector",
      "name": "Material Selector",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Section Heading",
          "default": "Select Material Type"
        },
        {
          "type": "text",
          "id": "material_1_name",
          "label": "Material 1 Name",
          "default": "Basic"
        },
        {
          "type": "number",
          "id": "material_1_price",
          "label": "Material 1 Price (per sq.ft)",
          "default": 10
        },
        {
          "type": "textarea",
          "id": "material_1_description",
          "label": "Material 1 Description",
          "default": "Standard quality wallpaper, perfect for most rooms"
        },
        {
          "type": "text",
          "id": "material_2_name",
          "label": "Material 2 Name",
          "default": "Premium"
        },
        {
          "type": "number",
          "id": "material_2_price",
          "label": "Material 2 Price (per sq.ft)",
          "default": 15
        },
        {
          "type": "textarea",
          "id": "material_2_description",
          "label": "Material 2 Description",
          "default": "High-quality wallpaper with enhanced durability"
        },
        {
          "type": "text",
          "id": "material_3_name",
          "label": "Material 3 Name",
          "default": "Luxury"
        },
        {
          "type": "number",
          "id": "material_3_price",
          "label": "Material 3 Price (per sq.ft)",
          "default": 20
        },
        {
          "type": "textarea",
          "id": "material_3_description",
          "label": "Material 3 Description",
          "default": "Premium luxury wallpaper with superior finish"
        }
      ]
    },
    {
      "type": "custom_dimensions",
      "name": "Wallpaper Dimensions",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Section Heading",
          "default": "Enter Wall Dimensions"
        },
        {
          "type": "text",
          "id": "width_label",
          "label": "Width Label",
          "default": "Width"
        },
        {
          "type": "text",
          "id": "height_label",
          "label": "Height Label",
          "default": "Height"
        },
        {
          "type": "number",
          "id": "width_min",
          "label": "Width Minimum",
          "default": 1
        },
        {
          "type": "number",
          "id": "width_max",
          "label": "Width Maximum",
          "default": 50
        },
        {
          "type": "number",
          "id": "height_min",
          "label": "Height Minimum",
          "default": 1
        },
        {
          "type": "number",
          "id": "height_max",
          "label": "Height Maximum",
          "default": 20
        },
        {
          "type": "text",
          "id": "width_step",
          "label": "Width Step",
          "default": "0.1"
        },
        {
          "type": "text",
          "id": "height_step",
          "label": "Height Step",
          "default": "0.1"
        },
        {
          "type": "text",
          "id": "width_placeholder",
          "label": "Width Placeholder",
          "default": "e.g. 10"
        },
        {
          "type": "text",
          "id": "height_placeholder",
          "label": "Height Placeholder",
          "default": "e.g. 8"
        }
      ]
    },
    {
      "type": "wallpaper_test",
      "name": "🧪 Wallpaper Detection",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Debug block showing wallpaper detection status and product info."
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_sticky_info",
      "default": true,
      "label": "t:sections.main-product.settings.enable_sticky_info.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.main-product.settings.header.content",
      "info": "t:sections.main-product.settings.header.info"
    },
    {
      "type": "select",
      "id": "media_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.main-product.settings.media_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.media_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.main-product.settings.media_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.main-product.settings.media_size.label",
      "info": "t:sections.main-product.settings.media_size.info"
    },
    {
      "type": "checkbox",
      "id": "constrain_to_viewport",
      "default": true,
      "label": "t:sections.main-product.settings.constrain_to_viewport.label"
    },
    {
      "type": "select",
      "id": "media_fit",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.main-product.settings.media_fit.options__1.label"
        },
        {
          "value": "cover",
          "label": "t:sections.main-product.settings.media_fit.options__2.label"
        }
      ],
      "default": "contain",
      "label": "t:sections.main-product.settings.media_fit.label"
    },
    {
      "type": "select",
      "id": "gallery_layout",
      "options": [
        {
          "value": "stacked",
          "label": "t:sections.main-product.settings.gallery_layout.options__1.label"
        },
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.gallery_layout.options__2.label"
        },
        {
          "value": "thumbnail",
          "label": "t:sections.main-product.settings.gallery_layout.options__3.label"
        },
        {
          "value": "thumbnail_slider",
          "label": "t:sections.main-product.settings.gallery_layout.options__4.label"
        }
      ],
      "default": "stacked",
      "label": "t:sections.main-product.settings.gallery_layout.label"
    },
    {
      "type": "select",
      "id": "media_position",
      "options": [
        {
          "value": "left",
          "label": "t:sections.main-product.settings.media_position.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.main-product.settings.media_position.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.main-product.settings.media_position.label",
      "info": "t:sections.main-product.settings.media_position.info"
    },
    {
      "type": "select",
      "id": "image_zoom",
      "options": [
        {
          "value": "lightbox",
          "label": "t:sections.main-product.settings.image_zoom.options__1.label"
        },
        {
          "value": "hover",
          "label": "t:sections.main-product.settings.image_zoom.options__2.label"
        },
        {
          "value": "none",
          "label": "t:sections.main-product.settings.image_zoom.options__3.label"
        }
      ],
      "default": "lightbox",
      "label": "t:sections.main-product.settings.image_zoom.label",
      "info": "t:sections.main-product.settings.image_zoom.info"
    },
    {
      "type": "select",
      "id": "mobile_thumbnails",
      "options": [
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__1.label"
        },
        {
          "value": "show",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__2.label"
        },
        {
          "value": "hide",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__3.label"
        }
      ],
      "default": "hide",
      "label": "t:sections.main-product.settings.mobile_thumbnails.label"
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "default": false,
      "label": "t:sections.main-product.settings.hide_variants.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "t:sections.main-product.settings.enable_video_looping.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
