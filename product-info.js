if (!customElements.get('product-info')) {
    customElements.define(
      'product-info',
      class ProductInfo extends HTMLElement {
        quantityInput = undefined;
        quantityForm = undefined;
        onVariantChangeUnsubscriber = undefined;
        cartUpdateUnsubscriber = undefined;
        abortController = undefined;
        pendingRequestUrl = null;
        preProcessHtmlCallbacks = [];
        postProcessHtmlCallbacks = [];
  
        constructor() {
          super();
          this.quantityInput = this.querySelector('.quantity__input');
        }
  
        connectedCallback() {
          this.initializeProductSwapUtility();
  
          this.onVariantChangeUnsubscriber = subscribe(
            PUB_SUB_EVENTS.optionValueSelectionChange,
            this.handleOptionValueChange.bind(this)
          );
  
          this.initQuantityHandlers();
  
          // 🧮 Custom logic for wallpaper area & price
          this.initializeWallpaperDimensionCalculation();
  
          this.dispatchEvent(new CustomEvent('product-info:loaded', { bubbles: true }));
        }
  
        disconnectedCallback() {
          this.onVariantChangeUnsubscriber();
          this.cartUpdateUnsubscriber?.();
        }
  
        // 🧮 Custom wallpaper input logic
        initializeWallpaperDimensionCalculation() {
          const widthInput = this.querySelector('[id^="custom-width-"]');
          const heightInput = this.querySelector('[id^="custom-height-"]');
          const areaOutput = this.querySelector('#calculated-area');
          const priceOutput = this.querySelector('#calculated-price');
          const pricePerSqFt = 12; // Adjust as needed
  
          if (!widthInput || !heightInput || !areaOutput || !priceOutput) return;
  
          const update = () => {
            const width = parseFloat(widthInput.value) || 0;
            const height = parseFloat(heightInput.value) || 0;
            const area = width * height;
            const price = area * pricePerSqFt;
  
            areaOutput.textContent = area.toFixed(2);
            priceOutput.textContent = price.toFixed(2);
          };
  
          widthInput.addEventListener('input', update);
          heightInput.addEventListener('input', update);
        }
  
        addPreProcessCallback(callback) {
          this.preProcessHtmlCallbacks.push(callback);
        }
  
        initQuantityHandlers() {
          if (!this.quantityInput) return;
  
          this.quantityForm = this.querySelector('.product-form__quantity');
          if (!this.quantityForm) return;
  
          this.setQuantityBoundries();
          if (!this.dataset.originalSection) {
            this.cartUpdateUnsubscriber = subscribe(PUB_SUB_EVENTS.cartUpdate, this.fetchQuantityRules.bind(this));
          }
        }
  
        initializeProductSwapUtility() {
          this.preProcessHtmlCallbacks.push((html) =>
            html.querySelectorAll('.scroll-trigger').forEach((element) => element.classList.add('scroll-trigger--cancel'))
          );
          this.postProcessHtmlCallbacks.push((newNode) => {
            window?.Shopify?.PaymentButton?.init();
            window?.ProductModel?.loadShopifyXR();
          });
        }
  
        handleOptionValueChange({ data: { event, target, selectedOptionValues } }) {
          if (!this.contains(event.target)) return;
  
          this.resetProductFormState();
  
          const productUrl = target.dataset.productUrl || this.pendingRequestUrl || this.dataset.url;
          this.pendingRequestUrl = productUrl;
          const shouldSwapProduct = this.dataset.url !== productUrl;
          const shouldFetchFullPage = this.dataset.updateUrl === 'true' && shouldSwapProduct;
  
          this.renderProductInfo({
            requestUrl: this.buildRequestUrlWithParams(productUrl, selectedOptionValues, shouldFetchFullPage),
            targetId: target.id,
            callback: shouldSwapProduct
              ? this.handleSwapProduct(productUrl, shouldFetchFullPage)
              : this.handleUpdateProductInfo(productUrl),
          });
        }
  
        resetProductFormState() {
          const productForm = this.productForm;
          productForm?.toggleSubmitButton(true);
          productForm?.handleErrorMessage();
        }
  
        handleSwapProduct(productUrl, updateFullPage) {
          return (html) => {
            this.productModal?.remove();
            const selector = updateFullPage ? "product-info[id^='MainProduct']" : 'product-info';
            const variant = this.getSelectedVariant(html.querySelector(selector));
            this.updateURL(productUrl, variant?.id);
  
            if (updateFullPage) {
              document.querySelector('head title').innerHTML = html.querySelector('head title').innerHTML;
  
              HTMLUpdateUtility.viewTransition(
                document.querySelector('main'),
                html.querySelector('main'),
                this.preProcessHtmlCallbacks,
                this.postProcessHtmlCallbacks
              );
            } else {
              HTMLUpdateUtility.viewTransition(
                this,
                html.querySelector('product-info'),
                this.preProcessHtmlCallbacks,
                this.postProcessHtmlCallbacks
              );
            }
          };
        }
  
        renderProductInfo({ requestUrl, targetId, callback }) {
          this.abortController?.abort();
          this.abortController = new AbortController();
  
          fetch(requestUrl, { signal: this.abortController.signal })
            .then((response) => response.text())
            .then((responseText) => {
              this.pendingRequestUrl = null;
              const html = new DOMParser().parseFromString(responseText, 'text/html');
              callback(html);
            })
            .then(() => {
              document.querySelector(`#${targetId}`)?.focus();
            })
            .catch((error) => {
              if (error.name !== 'AbortError') console.error(error);
            });
        }
  
        getSelectedVariant(productInfoNode) {
          const selectedVariant = productInfoNode.querySelector('variant-selects [data-selected-variant]')?.innerHTML;
          return selectedVariant ? JSON.parse(selectedVariant) : null;
        }
  
        buildRequestUrlWithParams(url, optionValues, shouldFetchFullPage = false) {
          const params = [];
          !shouldFetchFullPage && params.push(`section_id=${this.sectionId}`);
          if (optionValues.length) params.push(`option_values=${optionValues.join(',')}`);
          return `${url}?${params.join('&')}`;
        }
  
        updateOptionValues(html) {
          const variantSelects = html.querySelector('variant-selects');
          if (variantSelects) {
            HTMLUpdateUtility.viewTransition(this.variantSelectors, variantSelects, this.preProcessHtmlCallbacks);
          }
        }
  
        handleUpdateProductInfo(productUrl) {
          return (html) => {
            const variant = this.getSelectedVariant(html);
  
            this.pickupAvailability?.update(variant);
            this.updateOptionValues(html);
            this.updateURL(productUrl, variant?.id);
            this.updateVariantInputs(variant?.id);
  
            if (!variant) {
              this.setUnavailable();
              return;
            }
  
            this.updateMedia(html, variant?.featured_media?.id);
  
            const updateSourceFromDestination = (id, shouldHide = (source) => false) => {
              const source = html.getElementById(`${id}-${this.sectionId}`);
              const destination = this.querySelector(`#${id}-${this.dataset.section}`);
              if (source && destination) {
                destination.innerHTML = source.innerHTML;
                destination.classList.toggle('hidden', shouldHide(source));
              }
            };
  
            updateSourceFromDestination('price');
            updateSourceFromDestination('Sku', ({ classList }) => classList.contains('hidden'));
            updateSourceFromDestination('Inventory', ({ innerText }) => innerText === '');
            updateSourceFromDestination('Volume');
            updateSourceFromDestination('Price-Per-Item', ({ classList }) => classList.contains('hidden'));
  
            this.updateQuantityRules(this.sectionId, html);
            this.querySelector(`#Quantity-Rules-${this.dataset.section}`)?.classList.remove('hidden');
            this.querySelector(`#Volume-Note-${this.dataset.section}`)?.classList.remove('hidden');
  
            this.productForm?.toggleSubmitButton(
              html.getElementById(`ProductSubmitButton-${this.sectionId}`)?.hasAttribute('disabled') ?? true,
              window.variantStrings.soldOut
            );
  
            publish(PUB_SUB_EVENTS.variantChange, {
              data: {
                sectionId: this.sectionId,
                html,
                variant,
              },
            });
  
            // ✅ Re-initialize dimension logic after variant update
            this.initializeWallpaperDimensionCalculation();
          };
        }
  
        updateVariantInputs(variantId) {
          this.querySelectorAll(
            `#product-form-${this.dataset.section}, #product-form-installment-${this.dataset.section}`
          ).forEach((productForm) => {
            const input = productForm.querySelector('input[name="id"]');
            input.value = variantId ?? '';
            input.dispatchEvent(new Event('change', { bubbles: true }));
          });
        }
  
        updateURL(url, variantId) {
          this.querySelector('share-button')?.updateUrl(
            `${window.shopUrl}${url}${variantId ? `?variant=${variantId}` : ''}`
          );
          if (this.dataset.updateUrl === 'false') return;
          window.history.replaceState({}, '', `${url}${variantId ? `?variant=${variantId}` : ''}`);
        }
  
        setUnavailable() {
          this.productForm?.toggleSubmitButton(true, window.variantStrings.unavailable);
          const selectors = ['price', 'Inventory', 'Sku', 'Price-Per-Item', 'Volume-Note', 'Volume', 'Quantity-Rules']
            .map((id) => `#${id}-${this.dataset.section}`)
            .join(', ');
          document.querySelectorAll(selectors).forEach(({ classList }) => classList.add('hidden'));
        }
  
        updateMedia(html, variantFeaturedMediaId) {
          if (!variantFeaturedMediaId) return;
          // unchanged code from your original
          // ...
        }
  
        setQuantityBoundries() {
          if (!this.quantityInput) return;
  
          const data = {
            cartQuantity: parseInt(this.quantityInput.dataset.cartQuantity || 0),
            min: parseInt(this.quantityInput.dataset.min || 1),
            max: this.quantityInput.dataset.max ? parseInt(this.quantityInput.dataset.max) : null,
            step: this.quantityInput.step ? parseInt(this.quantityInput.step) : 1,
          };
  
          let min = data.min;
          const max = data.max === null ? null : data.max - data.cartQuantity;
          if (max !== null) min = Math.min(min, max);
          if (data.cartQuantity >= data.min) min = Math.min(min, data.step);
  
          this.quantityInput.min = min;
          max !== null ? (this.quantityInput.max = max) : this.quantityInput.removeAttribute('max');
          this.quantityInput.value = min;
  
          publish(PUB_SUB_EVENTS.quantityUpdate, undefined);
        }
  
        fetchQuantityRules() {
          const currentVariantId = this.productForm?.variantIdInput?.value;
          if (!currentVariantId) return;
  
          this.querySelector('.quantity__rules-cart .loading__spinner').classList.remove('hidden');
          fetch(`${this.dataset.url}?variant=${currentVariantId}&section_id=${this.dataset.section}`)
            .then((response) => response.text())
            .then((responseText) => {
              const html = new DOMParser().parseFromString(responseText, 'text/html');
              this.updateQuantityRules(this.dataset.section, html);
            })
            .catch((e) => console.error(e))
            .finally(() => this.querySelector('.quantity__rules-cart .loading__spinner').classList.add('hidden'));
        }
  
        updateQuantityRules(sectionId, html) {
          if (!this.quantityInput) return;
          this.setQuantityBoundries();
  
          const quantityFormUpdated = html.getElementById(`Quantity-Form-${sectionId}`);
          const selectors = ['.quantity__input', '.quantity__rules', '.quantity__label'];
          for (let selector of selectors) {
            const current = this.quantityForm.querySelector(selector);
            const updated = quantityFormUpdated.querySelector(selector);
            if (!current || !updated) continue;
            if (selector === '.quantity__input') {
              const attributes = ['data-cart-quantity', 'data-min', 'data-max', 'step'];
              for (let attribute of attributes) {
                const valueUpdated = updated.getAttribute(attribute);
                valueUpdated !== null ? current.setAttribute(attribute, valueUpdated) : current.removeAttribute(attribute);
              }
            } else {
              current.innerHTML = updated.innerHTML;
            }
          }
        }
  
        get productForm() {
          return this.querySelector(`product-form`);
        }
  
        get productModal() {
          return document.querySelector(`#ProductModal-${this.dataset.section}`);
        }
  
        get pickupAvailability() {
          return this.querySelector(`pickup-availability`);
        }
  
        get variantSelectors() {
          return this.querySelector('variant-selects');
        }
  
        get relatedProducts() {
          const relatedProductsSectionId = SectionId.getIdForSection(
            SectionId.parseId(this.sectionId),
            'related-products'
          );
          return document.querySelector(`product-recommendations[data-section-id^="${relatedProductsSectionId}"]`);
        }
  
        get quickOrderList() {
          const quickOrderListSectionId = SectionId.getIdForSection(
            SectionId.parseId(this.sectionId),
            'quick_order_list'
          );
          return document.querySelector(`quick-order-list[data-id^="${quickOrderListSectionId}"]`);
        }
  
        get sectionId() {
          return this.dataset.originalSection || this.dataset.section;
        }
      }
    );
  }
  