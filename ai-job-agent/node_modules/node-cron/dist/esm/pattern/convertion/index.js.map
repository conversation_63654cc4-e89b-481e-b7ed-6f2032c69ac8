{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/pattern/convertion/index.ts"], "names": [], "mappings": ";;;;;AAAA,sFAA4D;AAC5D,4FAAiE;AACjE,kGAAsE;AACtE,0EAA+C;AAE/C,kBAAe,CAAC,GAAG,EAAE;IAEjB,SAAS,uBAAuB,CAAC,WAAW;QACxC,IAAG,WAAW,CAAC,MAAM,KAAK,CAAC,EAAC,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,SAAS,YAAY,CAAC,GAAG;QACrB,OAAO,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;IAC9C,CAAC;IAGD,SAAS,iBAAiB,CAAC,WAAW;QAClC,KAAK,IAAI,CAAC,GAAC,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAC,CAAC;YACvC,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAC,CAAC,EAAE,CAAC,GAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAC,CAAC;gBACjC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC;YACD,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QAC7B,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAmBD,SAAS,UAAU,CAAC,UAAU;QAC1B,IAAI,WAAW,GAAG,YAAY,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC3D,WAAW,GAAG,uBAAuB,CAAC,WAAW,CAAC,CAAC;QACnD,WAAW,CAAC,CAAC,CAAC,GAAG,IAAA,gCAAoB,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,WAAW,CAAC,CAAC,CAAC,GAAG,IAAA,mCAAsB,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,WAAW,GAAG,IAAA,sCAAwB,EAAC,WAAW,CAAC,CAAC;QACpD,WAAW,GAAG,IAAA,0BAAa,EAAC,WAAW,CAAC,CAAC;QAEzC,WAAW,GAAG,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAE7C,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,OAAO,UAAU,CAAC;AACtB,CAAC,CAAC,EAAE,CAAC"}