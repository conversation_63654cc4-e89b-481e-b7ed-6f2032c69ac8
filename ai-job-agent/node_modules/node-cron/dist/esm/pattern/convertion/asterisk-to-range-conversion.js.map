{"version": 3, "file": "asterisk-to-range-conversion.js", "sourceRoot": "", "sources": ["../../../../src/pattern/convertion/asterisk-to-range-conversion.ts"], "names": [], "mappings": ";;AACA,kBAAe,CAAC,GAAG,EAAE;IACjB,SAAS,eAAe,CAAC,UAAU,EAAE,WAAW;QAC5C,IAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC;YAC/B,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,SAAS,wBAAwB,CAAC,WAAW;QACzC,WAAW,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACzD,WAAW,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACzD,WAAW,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACzD,WAAW,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACzD,WAAW,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QACzD,WAAW,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,OAAO,wBAAwB,CAAC;AACpC,CAAC,CAAC,EAAE,CAAC"}